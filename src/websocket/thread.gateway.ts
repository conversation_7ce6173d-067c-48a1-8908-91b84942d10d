import { Server, Socket } from 'socket.io';
import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ThreadsService } from '../modules/threads/threads.service';
import { CustomLogger } from '../common/logging';

type JoinThreadRequest = {
  threadId: string;
  userId: string;
};

type SendMessageRequest = {
  threadId: string;
  userId: string;
  message: {
    status: string;
    message: string;
    timestamp: string;
  };
};

interface AuthenticatedSocket extends Socket {
  user?: {
    id: string;
    email: string;
    name: string;
  };
}

interface JwtPayload {
  sub: string;
  email: string;
  name: string;
  iat?: number;
  exp?: number;
}

@Injectable()
export class ThreadGateway {
  private threadsService: ThreadsService;

  constructor(
    private readonly jwtService: JwtService,
    private readonly logger: CustomLogger,
  ) {
    // Create a simple logger for WebSocket gateway
    const simpleLogger = {
      log: (message: string) => console.log(message),
      error: (message: string, _meta?: any, stack?: string) =>
        console.error(message, stack),
      warn: (message: string) => console.warn(message),
      debug: (message: string) => console.debug(message),
    };
    this.threadsService = new ThreadsService(simpleLogger as CustomLogger);
  }

  // JWT Authentication middleware
  private async authenticateSocket(
    socket: AuthenticatedSocket,
  ): Promise<boolean> {
    try {
      // Get token from handshake auth, headers, or query
      const token =
        socket.handshake.auth.token ||
        socket.handshake.headers.authorization?.replace('Bearer ', '') ||
        (socket.handshake.query.token as string);

      if (!token) {
        await socket.emit(
          'error',
          JSON.stringify({
            type: 'AuthenticationError',
            message: 'No token provided',
          }),
        );
        return false;
      }

      // Verify JWT token
      const payload = (await this.jwtService.verifyAsync(token)) as JwtPayload;

      // Attach user info to socket
      socket.user = {
        id: payload.sub,
        email: payload.email,
        name: payload.name,
      };

      console.log(
        `WebSocket authenticated: ${socket.user.name} (${socket.user.id})`,
      );
      return true;
    } catch (authError) {
      await socket.emit(
        'error',
        JSON.stringify({
          type: 'AuthenticationError',
          message: 'Invalid token',
        }),
      );
      return false;
    }
  }

  // Handle connection and setup event listeners
  public async handleConnection(io: Server, socket: AuthenticatedSocket) {
    console.log(`Thread Gateway: ${socket.id} connected`);

    try {
      // Authenticate socket first
      const isAuthenticated = await this.authenticateSocket(socket);
      if (!isAuthenticated) {
        socket.disconnect();
        return;
      }

      console.log(
        `WebSocket client authenticated: ${socket.user?.name} (${socket.id})`,
      );

      // Handle join thread
      socket.on('joinThread', async (data: any) => {
        try {
          // Verify user is authenticated
          if (!socket.user) {
            socket.emit(
              'error',
              JSON.stringify({
                type: 'AuthenticationError',
                message: 'User not authenticated',
              }),
            );
            return;
          }

          // Use authenticated user ID instead of provided userId
          const authenticatedData: JoinThreadRequest = {
            threadId: data.threadId as string,
            userId: socket.user.id, // Use authenticated user ID
          };

          await this.threadsService.handleJoinThread(
            io,
            socket,
            authenticatedData,
          );
        } catch (error) {
          console.error('JoinThread error:', error);
          socket.emit(
            'error',
            JSON.stringify({
              type: 'UnexpectedError',
              message: 'Failed to join thread',
            }),
          );
        }
      });

      // Handle send message
      socket.on('sendThreadMessage', async (data: any) => {
        try {
          // Verify user is authenticated
          if (!socket.user) {
            socket.emit(
              'error',
              JSON.stringify({
                type: 'AuthenticationError',
                message: 'User not authenticated',
              }),
            );
            return;
          }

          // Use authenticated user ID instead of provided userId
          const authenticatedData: SendMessageRequest = {
            threadId: data.threadId as string,
            userId: socket.user.id, // Use authenticated user ID
            message: data.message as SendMessageRequest['message'],
          };

          await this.threadsService.handleSendMessage(
            io,
            socket,
            authenticatedData,
          );
        } catch (error) {
          console.error('SendThreadMessage error:', error);
          socket.emit(
            'error',
            JSON.stringify({
              type: 'UnexpectedError',
              message: 'Failed to send message',
            }),
          );
        }
      });

      // Handle leave thread
      socket.on('leaveThread', (threadId: string) => {
        try {
          void socket.leave(threadId);
          console.log(
            `User ${socket.user?.name || socket.id} left thread ${threadId}`,
          );

          socket.emit(
            'leaveThreadResult',
            JSON.stringify({
              type: 'LeaveThreadSuccess',
              result: { threadId },
            }),
          );
        } catch (error) {
          console.error('LeaveThread error:', error);
          socket.emit(
            'error',
            JSON.stringify({
              type: 'UnexpectedError',
              message: 'Failed to leave thread',
            }),
          );
        }
      });

      // Handle typing indicator
      socket.on(
        'typing',
        (data: { threadId: string; userId: string; isTyping: boolean }) => {
          try {
            const { threadId, isTyping } = data;
            const userId = socket.user?.id || data.userId;

            if (isTyping) {
              socket.to(threadId).emit('userTyping', {
                userId,
                threadId,
                isTyping: true,
              });
            } else {
              socket.to(threadId).emit('userTyping', {
                userId,
                threadId,
                isTyping: false,
              });
            }
          } catch (error) {
            console.error('Typing indicator error:', error);
          }
        },
      );

      // Handle read receipts
      socket.on(
        'markAsRead',
        (data: { threadId: string; messageId: string; userId: string }) => {
          try {
            const { threadId, messageId } = data;
            const userId = socket.user?.id || data.userId;

            socket.to(threadId).emit('messageRead', {
              messageId,
              userId,
              threadId,
              readAt: new Date().toISOString(),
            });
          } catch (error) {
            console.error('Mark as read error:', error);
          }
        },
      );

      // Handle disconnect
      socket.on('disconnect', () => {
        console.log(
          `Thread Gateway: ${socket.user?.name || socket.id} disconnected`,
        );
      });
    } catch (error) {
      console.error(`WebSocket connection error: ${error}`);
      socket.disconnect();
    }
  }

  // Method to broadcast to all users in a thread
  public broadcastToThread(
    io: Server,
    threadId: string,
    event: string,
    data: any,
  ) {
    io.to(threadId).emit(event, data);
  }

  // Method to send to specific user
  public sendToUser(io: Server, userId: string, event: string, data: any) {
    io.to(userId).emit(event, data);
  }

  // Method to get online users in a thread
  public getOnlineUsersInThread(io: Server, threadId: string) {
    const room = io.sockets.adapter.rooms.get(threadId);
    return room ? Array.from(room) : [];
  }
}

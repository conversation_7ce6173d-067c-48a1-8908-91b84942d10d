// src/auth/auth.service.ts
import { Injectable, HttpStatus, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { AuthDto } from './dto/auth.dto';
import { JwtPayload } from './jwt-payload.interface';
import { UsersService } from '../users/users.service';
import { CustomLogger } from '../../common/logging';
import { I18nService } from 'nestjs-i18n';

import { HTTP } from '../../common/http';
import * as bcrypt from 'bcrypt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthService extends HTTP {
  private readonly loggerMeta: any;

  constructor(
    private readonly jwtService: JwtService,
    private readonly usersService: UsersService,
    private readonly logger: CustomLogger,
    private readonly i18n: I18nService,
    private readonly configService: ConfigService,
  ) {
    super();
    this.loggerMeta = { context: AuthService.name };
  }

  async handleADFSLogin(profile: any) {
    // 1. Check user by email
    let user = await this.usersService.findByUsername(profile.emails[0]?.value);
    if (!user) {
      // User does not exist, create new
      const createUserDto = {
        name: profile.displayName || profile.name?.givenName || '',
        email: profile.emails[0]?.value,
        isAdmin: false,
        isInstructor: false,
        gender: 'Male', // default, or map from profile if available
        password: '', // or random string
      };
      user = await this.usersService.createUser(createUserDto);
    }
    if (!user) {
      throw new Error('User not found or could not be created');
    }
    // 2. Generate JWT
    const payload = {
      userId: user.id,
      email: user.email,
      name: user.name,
      isAdmin: user.isAdmin,
      isInstructor: user.isInstructor,
    };
    const token = this.jwtService.sign(payload);
    return {
      accessToken: token,
      id: user.id,
      displayName: user.name,
      email: user.email,
      expiresAt: Date.now() + 3600 * 1000,
    };
  }
}

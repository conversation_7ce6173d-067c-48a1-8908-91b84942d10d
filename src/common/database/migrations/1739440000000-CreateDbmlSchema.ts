import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class CreateDbmlSchema1739440000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add missing fields to users table to match DBML schema
    await queryRunner.query(`
      ALTER TABLE users ADD COLUMN IF NOT EXISTS avatar_url varchar;
    `);

    // Create course table
    await queryRunner.createTable(
      new Table({
        name: 'course',
        columns: [
          {
            name: 'id',
            type: 'serial',
            isPrimary: true,
          },
          {
            name: 'name',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create group_assignment table
    await queryRunner.createTable(
      new Table({
        name: 'group_assignment',
        columns: [
          {
            name: 'id',
            type: 'serial',
            isPrimary: true,
          },
          {
            name: 'course_id',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create groupuser table
    await queryRunner.createTable(
      new Table({
        name: 'groupuser',
        columns: [
          {
            name: 'id',
            type: 'serial',
            isPrimary: true,
          },
          {
            name: 'group_id',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'role',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'joined_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create document table
    await queryRunner.createTable(
      new Table({
        name: 'document',
        columns: [
          {
            name: 'id',
            type: 'serial',
            isPrimary: true,
          },
          {
            name: 'uploader_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'group_id',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'file_url',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'file_name',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create course_user table
    await queryRunner.createTable(
      new Table({
        name: 'course_user',
        columns: [
          {
            name: 'id',
            type: 'serial',
            isPrimary: true,
          },
          {
            name: 'course_id',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create group_assignment_course table
    await queryRunner.createTable(
      new Table({
        name: 'group_assignment_course',
        columns: [
          {
            name: 'id',
            type: 'serial',
            isPrimary: true,
          },
          {
            name: 'group_id',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'course_id',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create setting table
    await queryRunner.createTable(
      new Table({
        name: 'setting',
        columns: [
          {
            name: 'id',
            type: 'serial',
            isPrimary: true,
          },
        ],
      }),
      true,
    );

    // Add foreign key constraints based on DBML relationships

    // group_assignment.created_by > user.id
    await queryRunner.createForeignKey(
      'group_assignment',
      new TableForeignKey({
        columnNames: ['created_by'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
      }),
    );

    // group_assignment.course_id > course.id
    await queryRunner.createForeignKey(
      'group_assignment',
      new TableForeignKey({
        columnNames: ['course_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'course',
        onDelete: 'CASCADE',
      }),
    );

    // groupuser.group_id > group_assignment.id
    await queryRunner.createForeignKey(
      'groupuser',
      new TableForeignKey({
        columnNames: ['group_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'group_assignment',
        onDelete: 'CASCADE',
      }),
    );

    // groupuser.user_id > user.id
    await queryRunner.createForeignKey(
      'groupuser',
      new TableForeignKey({
        columnNames: ['user_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
      }),
    );

    // document.uploader_id > user.id
    await queryRunner.createForeignKey(
      'document',
      new TableForeignKey({
        columnNames: ['uploader_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
      }),
    );

    // document.group_id > group_assignment.id
    await queryRunner.createForeignKey(
      'document',
      new TableForeignKey({
        columnNames: ['group_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'group_assignment',
        onDelete: 'CASCADE',
      }),
    );

    // course_user.course_id > course.id
    await queryRunner.createForeignKey(
      'course_user',
      new TableForeignKey({
        columnNames: ['course_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'course',
        onDelete: 'CASCADE',
      }),
    );

    // course_user.user_id > user.id
    await queryRunner.createForeignKey(
      'course_user',
      new TableForeignKey({
        columnNames: ['user_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
      }),
    );

    // group_assignment_course.group_id > group_assignment.id
    await queryRunner.createForeignKey(
      'group_assignment_course',
      new TableForeignKey({
        columnNames: ['group_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'group_assignment',
        onDelete: 'CASCADE',
      }),
    );

    // group_assignment_course.course_id > course.id
    await queryRunner.createForeignKey(
      'group_assignment_course',
      new TableForeignKey({
        columnNames: ['course_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'course',
        onDelete: 'CASCADE',
      }),
    );

    // group_assignment_course.created_by > user.id
    await queryRunner.createForeignKey(
      'group_assignment_course',
      new TableForeignKey({
        columnNames: ['created_by'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints first
    const tables = [
      'group_assignment_course',
      'course_user',
      'document',
      'groupuser',
      'group_assignment',
    ];

    // Drop all foreign keys for each table
    for (const tableName of tables) {
      const table = await queryRunner.getTable(tableName);
      if (table) {
        const foreignKeys = table.foreignKeys;
        for (const foreignKey of foreignKeys) {
          await queryRunner.dropForeignKey(tableName, foreignKey);
        }
      }
    }

    // Drop tables in reverse order to handle dependencies
    await queryRunner.dropTable('setting');
    await queryRunner.dropTable('group_assignment_course');
    await queryRunner.dropTable('course_user');
    await queryRunner.dropTable('document');
    await queryRunner.dropTable('groupuser');
    await queryRunner.dropTable('group_assignment');
    await queryRunner.dropTable('course');

    // Remove added column from users table
    await queryRunner.query(`
      ALTER TABLE users DROP COLUMN IF EXISTS avatar_url;
    `);
  }
}

# PostgreSQL Migration Summary

## Overview
Created a new migration file `1739440000000-CreateDbmlSchema.ts` to implement the complete database schema as defined in `file.dbml`, while preserving the existing PostgreSQL user table structure.

## What Was Implemented

### 1. Updated Users Table
- **Added**: `avatar_url` field (varchar) to match DBML schema
- **Preserved**: All existing fields from the current users table:
  - `id` (uuid) - kept as UUID instead of int for consistency with existing system
  - `name`, `email` - already existed
  - `gender`, `phone_number`, `is_admin`, `is_instructor` - preserved existing fields
  - `created_at`, `updated_at`, `deleted_at` - preserved existing timestamp fields

### 2. New Tables Created
All tables follow the DBML schema with appropriate data types and constraints:

- **`course`**: Course management with id, name, created_at
- **`group_assignment`**: Group assignments linked to courses and users
- **`groupuser`**: Junction table for group membership with roles
- **`document`**: File management for groups with uploader tracking
- **`course_user`**: Junction table for course enrollment
- **`group_assignment_course`**: Junction table linking group assignments to courses
- **`setting`**: Basic settings table (minimal structure as per DBML)

### 3. Foreign Key Relationships
Implemented all relationships from the DBML schema:
- `group_assignment.created_by` → `users.id`
- `group_assignment.course_id` → `course.id`
- `groupuser.group_id` → `group_assignment.id`
- `groupuser.user_id` → `users.id`
- `document.uploader_id` → `users.id`
- `document.group_id` → `group_assignment.id`
- `course_user.course_id` → `course.id`
- `course_user.user_id` → `users.id`
- `group_assignment_course.group_id` → `group_assignment.id`
- `group_assignment_course.course_id` → `course.id`
- `group_assignment_course.created_by` → `users.id`

## Important Notes

### 1. Data Type Considerations
- **User IDs**: Kept as `uuid` to maintain compatibility with existing user table
- **Auto-increment IDs**: Used `serial` type for new tables as specified in DBML
- **Foreign Keys**: Mixed types (uuid for user references, int for other table references)

### 2. MongoDB Collections
- **`thread`** and **`message`** tables are defined in DBML but you mentioned they exist in MongoDB
- These were **NOT** created in PostgreSQL to avoid conflicts
- If you need to migrate these to PostgreSQL later, you can create a separate migration

### 3. Cascade Behavior
- All foreign keys use `CASCADE` on delete for data consistency
- This means deleting a parent record will automatically delete related child records

### 4. Migration Safety
- Uses `IF NOT EXISTS` for column additions to prevent errors on re-runs
- Proper rollback functionality in the `down()` method
- Foreign keys are dropped before tables in rollback to handle dependencies

## Next Steps

1. **Run the migration**:
   ```bash
   npm run migration:run
   # or
   yarn migration:run
   ```

2. **Verify the schema**:
   - Check that all tables were created successfully
   - Verify foreign key constraints are in place
   - Test basic CRUD operations

3. **Update Entity Classes**:
   - Create or update TypeORM entity classes to match the new schema
   - Ensure proper decorators for relationships

4. **Consider MongoDB Integration**:
   - Decide if `thread` and `message` should remain in MongoDB or be migrated to PostgreSQL
   - Update application logic to handle the hybrid database approach

## Rollback
If you need to rollback this migration:
```bash
npm run migration:revert
# or  
yarn migration:revert
```

This will safely remove all created tables and foreign keys, and remove the added `avatar_url` column from the users table.

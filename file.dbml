Table user {
  id int [pk]
  name varchar
  email varchar
  avatar_url varchar
  created_at datetime
}

Table course {
  id int [pk]
  name varchar
  created_at datetime
}

Table group_assignment {
  id int [pk]
  course_id int
  name varchar
  description text
  created_by int
  created_at datetime
}

Table thread {
  id int [pk]
  group_assignment_id int
  name varchar
  type varchar
  created_at datetime
}

Table groupuser {
  id int [pk]
  group_id int
  user_id int
  role varchar
  joined_at datetime
}

Table message {
  id int [pk]
  thread_id int // previously chatroom_id
  sender_id int
  content text
  message_type varchar
  created_at datetime
}

Table document {
  id int [pk]
  uploader_id int
  group_id int
  file_url varchar
  file_name varchar
  created_at datetime
}

Table course_user {
  id int [pk]
  course_id int
  user_id int
  name varchar
  created_at datetime
}

Table group_assignment_course {
  id int [pk]
  group_id int
  course_id int
  name varchar
  description text
  created_by int
  created_at datetime
}

Table setting {
  id int [pk]
}

Ref: group_assignment.created_by > user.id
Ref: group_assignment.course_id > course.id
Ref: thread.group_assignment_id > group_assignment.id
Ref: groupuser.group_id > group_assignment.id
Ref: groupuser.user_id > user.id
Ref: message.thread_id > thread.id
Ref: message.sender_id > user.id
Ref: document.uploader_id > user.id
Ref: document.group_id > group_assignment.id
Ref: course_user.course_id > course.id
Ref: course_user.user_id > user.id
Ref: group_assignment_course.group_id > group_assignment.id
Ref: group_assignment_course.course_id > course.id
